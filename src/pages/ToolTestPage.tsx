import React, { useState, useEffect, useCallback } from 'react';
import { galleryService } from '../lib/galleryService';
import { ImageData } from '../data/mockData';
import { SimpleImage } from '../components/SimpleImage';
import { toolController, ToolExecutionState, ToolExecutionSession } from '../controllers/ToolController';
import { ToolExecutionResult } from '../lib/vlImageToolsBridge';

interface ToolTestResult {
  success: boolean;
  data?: any;
  error?: string;
  executionTime?: number;
}

const ToolTestPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // 图片库相关状态
  const [libraryImages, setLibraryImages] = useState<ImageData[]>([]);
  const [selectedLibraryImage, setSelectedLibraryImage] = useState<ImageData | null>(null);
  const [showLibrarySelector, setShowLibrarySelector] = useState<boolean>(false);
  const [libraryLoading, setLibraryLoading] = useState<boolean>(false);
  const [imageSourceMode, setImageSourceMode] = useState<'upload' | 'library'>('upload');

  // 工具测试状态 - 使用新的ToolController
  const [currentSession, setCurrentSession] = useState<ToolExecutionSession | null>(null);
  const [executionHistory, setExecutionHistory] = useState<ToolExecutionState[]>([]);
  const [toolLoading, setToolLoading] = useState<boolean>(false);
  const [selectedExecution, setSelectedExecution] = useState<ToolExecutionState | null>(null);

  // 使用useEffect设置ToolController事件监听器
  useEffect(() => {
    const handleSessionStarted = (session: ToolExecutionSession) => {
      setCurrentSession(session);
    };

    const handleSessionUpdated = (session: ToolExecutionSession) => {
      setCurrentSession(session);
    };

    const handleExecutionStarted = (execution: ToolExecutionState) => {
      setToolLoading(true);
      setSelectedExecution(execution);
    };

    const handleExecutionCompleted = (execution: ToolExecutionState) => {
      setToolLoading(false);
      setSelectedExecution(execution);
      setExecutionHistory(toolController.getExecutionHistory());
    };

    const handleExecutionFailed = (execution: ToolExecutionState) => {
      setToolLoading(false);
      setSelectedExecution(execution);
      setExecutionHistory(toolController.getExecutionHistory());
    };

    // 注册事件监听器
    toolController.on('sessionStarted', handleSessionStarted);
    toolController.on('sessionUpdated', handleSessionUpdated);
    toolController.on('executionStarted', handleExecutionStarted);
    toolController.on('executionCompleted', handleExecutionCompleted);
    toolController.on('executionFailed', handleExecutionFailed);

    // 初始化状态
    setCurrentSession(toolController.getCurrentSession());
    setExecutionHistory(toolController.getExecutionHistory());

    return () => {
      // 清理事件监听器
      toolController.off('sessionStarted', handleSessionStarted);
      toolController.off('sessionUpdated', handleSessionUpdated);
      toolController.off('executionStarted', handleExecutionStarted);
      toolController.off('executionCompleted', handleExecutionCompleted);
      toolController.off('executionFailed', handleExecutionFailed);
    };
  }, []);

  // 获取可用工具列表 - 使用新的ToolController
  const getAvailableTools = () => {
    return toolController.getAvailableTools();
  };

  const availableTools = getAvailableTools();

  // 核心工具执行函数 - 使用新的ToolController
  const executeTool = useCallback(async (toolName: string, params: any = {}) => {
    try {
      console.log(`执行工具: ${toolName}`, params);
      const result = await toolController.executeTool(toolName, params);
      return result;
    } catch (error) {
      console.error(`工具 ${toolName} 执行失败:`, error);
      throw error;
    }
  }, []);

  // 加载图片库数据
  const loadLibraryImages = async () => {
    setLibraryLoading(true);
    try {
      const result = await galleryService.getImages({ limit: 50 });
      if (!result.error && result.images) {
        setLibraryImages(result.images);
      } else {
        console.error('加载图片库失败:', result.error);
      }
    } catch (error) {
      console.error('加载图片库失败:', error);
    } finally {
      setLibraryLoading(false);
    }
  };

  // 处理图片上传
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      alert('请选择图片文件');
      return;
    }

    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      alert('图片文件过大，请选择小于10MB的图片');
      return;
    }

    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setSelectedImage(result);
        setImagePreview(result);
        setSelectedLibraryImage(null);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('图片上传处理失败:', error);
      alert('图片处理失败，请重试');
    }
  };

  // 处理从图片库选择图片
  const handleLibraryImageSelect = (image: ImageData) => {
    setSelectedLibraryImage(image);
    setSelectedImage(null);
    setImagePreview(image.url);
    setShowLibrarySelector(false);
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">PathBasedImageToolsBridge 测试页面</h1>

      {/* 导航标签 */}
      <div className="mb-6 border-b">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'overview'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            系统概览
          </button>
          <button
            onClick={() => setActiveTab('testing')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'testing'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            工具测试
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'history'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            执行历史
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'analytics'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            统计分析
          </button>
        </nav>
      </div>

      {/* 系统概览标签 */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* 可用工具 */}
          <div className="p-4 border rounded-lg">
            <h2 className="text-lg font-semibold mb-2">可用工具 ({availableTools.length})</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {availableTools.map((tool, index) => (
                <div key={index} className="p-3 bg-gray-50 rounded border">
                  <div className="font-medium text-blue-600">{tool.name}</div>
                  <div className="text-sm text-gray-600 mt-1">{tool.description}</div>
                  <div className="text-xs text-gray-500 mt-2">
                    参数: {Object.keys(tool.parameters).join(', ') || '无'}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 图片选择区域 */}
          <div className="p-4 border rounded-lg">
            <h2 className="text-lg font-semibold mb-3">图片选择</h2>
            <div className="flex space-x-4 mb-4">
              <button
                onClick={() => setImageSourceMode('upload')}
                className={`px-4 py-2 rounded ${imageSourceMode === 'upload' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
              >
                上传新图片
              </button>
              <button
                onClick={() => setImageSourceMode('library')}
                className={`px-4 py-2 rounded ${imageSourceMode === 'library' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
              >
                从图片库选择
              </button>
            </div>

            {imageSourceMode === 'upload' && (
              <input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="w-full p-2 border rounded"
              />
            )}

            {imageSourceMode === 'library' && (
              <div>
                <button
                  onClick={() => {
                    setShowLibrarySelector(!showLibrarySelector);
                    if (!showLibrarySelector) {
                      loadLibraryImages();
                    }
                  }}
                  className="w-full p-2 border rounded bg-blue-50 hover:bg-blue-100 mb-3"
                >
                  {showLibrarySelector ? '隐藏图片库' : '显示图片库'}
                </button>

                {showLibrarySelector && (
                  <div className="max-h-48 overflow-y-auto border rounded p-3">
                    {libraryLoading ? (
                      <p className="text-center text-gray-500">正在加载图片库...</p>
                    ) : libraryImages.length === 0 ? (
                      <p className="text-center text-gray-500">图片库为空</p>
                    ) : (
                      <div className="grid grid-cols-4 gap-3">
                        {libraryImages.map((image) => (
                          <div
                            key={image.id}
                            onClick={() => handleLibraryImageSelect(image)}
                            className={`cursor-pointer border-2 rounded p-2 ${
                              selectedLibraryImage?.id === image.id ? 'border-blue-500' : 'border-gray-200'
                            }`}
                          >
                            <SimpleImage
                              imagePath={image.url}
                              alt={image.title}
                              className="w-full h-20 object-cover rounded"
                            />
                            <p className="text-xs text-center mt-1 truncate">{image.title}</p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* 显示当前选择的图片 */}
            {(imagePreview || selectedLibraryImage) && (
              <div className="mt-3 flex items-start space-x-4">
                <SimpleImage
                  imagePath={imagePreview || selectedLibraryImage?.url || ''}
                  alt="预览"
                  className="max-w-xs max-h-48 object-contain border rounded"
                />
                <div className="flex-1">
                  <p className="text-sm text-green-600 mb-2">
                    ✅ {selectedLibraryImage ? `图片库图片: ${selectedLibraryImage.title}` : '上传的图片'}
                  </p>
                  <button
                    onClick={() => {
                      setSelectedImage(null);
                      setImagePreview(null);
                      setSelectedLibraryImage(null);
                    }}
                    className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
                  >
                    移除图片
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 工具测试标签 */}
      {activeTab === 'testing' && (
        <div className="space-y-6">
          <div className="p-4 border rounded-lg">
            <h2 className="text-lg font-semibold mb-4">VL图像工具测试</h2>
            <p className="text-sm text-gray-600 mb-4">
              使用新的ToolController管理工具执行，支持会话管理、执行监控和历史记录。
            </p>

            {/* 当前会话状态 */}
            {currentSession && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-sm font-medium text-blue-800">
                      当前会话: {currentSession.id.slice(-8)}
                    </span>
                    <span className={`ml-2 px-2 py-1 text-xs rounded ${
                      currentSession.status === 'executing'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {currentSession.status === 'executing' ? '执行中' : '空闲'}
                    </span>
                  </div>
                  <button
                    onClick={() => toolController.endSession()}
                    className="text-xs px-2 py-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200"
                  >
                    结束会话
                  </button>
                </div>
                <div className="text-xs text-blue-600 mt-1">
                  执行次数: {currentSession.executions.length} |
                  开始时间: {currentSession.startTime.toLocaleTimeString()}
                </div>
              </div>
            )}

            {/* analyze_image 专用测试区域 */}
            <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
              <h3 className="text-md font-semibold mb-3 text-yellow-800">🔍 analyze_image 专用测试</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
                <button
                  onClick={() => executeTool('analyze_image', {
                    imagePath: '10042.jpg',
                    includeStructuredData: false
                  })}
                  disabled={toolLoading}
                  className="px-4 py-3 bg-yellow-600 text-white rounded hover:bg-yellow-700 disabled:bg-gray-400"
                >
                  基础分析 10042.jpg
                </button>

                <button
                  onClick={() => executeTool('analyze_image', {
                    imagePath: '10042.jpg',
                    includeStructuredData: true
                  })}
                  disabled={toolLoading}
                  className="px-4 py-3 bg-yellow-700 text-white rounded hover:bg-yellow-800 disabled:bg-gray-400"
                >
                  完整分析 10042.jpg
                </button>

                {selectedLibraryImage && (
                  <button
                    onClick={() => executeTool('analyze_image', {
                      imagePath: selectedLibraryImage.url,
                      includeStructuredData: true
                    })}
                    disabled={toolLoading}
                    className="px-4 py-3 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:bg-gray-400"
                  >
                    分析当前选择图片
                  </button>
                )}
              </div>

              <div className="text-xs text-yellow-600">
                💡 直接调用 PathBasedImageToolsBridge.executeTool('analyze_image', params)
              </div>
            </div>

            {/* 其他工具快速测试 */}
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded">
              <h3 className="text-md font-semibold mb-3 text-blue-800">🔧 其他工具测试</h3>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                <button
                  onClick={() => executeTool('find_images_by_tags', { tags: ['风景'], limit: 5 })}
                  disabled={toolLoading}
                  className="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400 text-sm"
                >
                  搜索风景图片
                </button>

                <button
                  onClick={() => executeTool('find_images_by_tags', { tags: ['动物'], limit: 5 })}
                  disabled={toolLoading}
                  className="px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-400 text-sm"
                >
                  搜索动物图片
                </button>

                <button
                  onClick={() => executeTool('find_similar_images_by_description', {
                    description: '美丽的日落场景',
                    threshold: 0.4,
                    limit: 5
                  })}
                  disabled={toolLoading}
                  className="px-3 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:bg-gray-400 text-sm"
                >
                  描述搜索图片
                </button>

                {selectedLibraryImage && (
                  <button
                    onClick={() => executeTool('get_image_analysis', {
                      imagePath: selectedLibraryImage.url
                    })}
                    disabled={toolLoading}
                    className="px-3 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 disabled:bg-gray-400 text-sm"
                  >
                    获取图片分析
                  </button>
                )}
              </div>
            </div>

            {/* 结果显示 */}
            <div className="mt-6">
              {toolLoading && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded">
                  <div className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="text-sm font-medium text-blue-800">正在执行工具: {toolName}...</span>
                  </div>
                </div>
              )}

              {toolResult && (
                <div className={`p-4 border rounded ${toolResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className={`text-sm font-medium ${toolResult.success ? 'text-green-800' : 'text-red-800'}`}>
                      {toolResult.success ? '✅ 执行成功' : '❌ 执行失败'}
                      {toolResult.executionTime && (
                        <span className="ml-2 text-xs">({toolResult.executionTime}ms)</span>
                      )}
                    </h4>
                    <button
                      onClick={() => setToolResult(null)}
                      className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded hover:bg-gray-200"
                    >
                      清除
                    </button>
                  </div>
                  <pre className={`text-xs whitespace-pre-wrap max-h-96 overflow-y-auto ${toolResult.success ? 'text-green-700' : 'text-red-700'}`}>
                    {JSON.stringify(toolResult.success ? toolResult.data : { error: toolResult.error }, null, 2)}
                  </pre>
                </div>
              )}

              {!toolLoading && !toolResult && (
                <div className="p-4 bg-gray-50 border border-gray-200 rounded text-center">
                  <div className="text-sm text-gray-500">
                    点击上方按钮执行工具调用
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    使用 PathBasedImageToolsBridge.executeTool() 直接调用
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ToolTestPage;