import { VLImageToolsBridge, ToolExecutionResult } from '../lib/vlImageToolsBridge';
import { ToolCall, ToolCallResponse, ToolResult } from '../types/tools';

/**
 * Tool execution state
 */
export interface ToolExecutionState {
  id: string;
  toolName: string;
  parameters: any;
  status: 'pending' | 'executing' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  result?: ToolExecutionResult;
  progress?: number;
}

/**
 * Tool execution session
 */
export interface ToolExecutionSession {
  id: string;
  executions: ToolExecutionState[];
  status: 'idle' | 'executing' | 'completed';
  startTime: Date;
  endTime?: Date;
}

/**
 * Tool Controller - 管理工具执行的状态和生命周期
 */
export class ToolController {
  private currentSession: ToolExecutionSession | null = null;
  private executionHistory: ToolExecutionState[] = [];
  private listeners: Map<string, Function[]> = new Map();

  constructor() {
    this.initializeEventListeners();
  }

  /**
   * 初始化事件监听器
   */
  private initializeEventListeners(): void {
    // 可以在这里添加全局事件监听器
  }

  /**
   * 开始新的执行会话
   */
  startSession(): string {
    const sessionId = this.generateId();
    this.currentSession = {
      id: sessionId,
      executions: [],
      status: 'idle',
      startTime: new Date()
    };
    
    this.emit('sessionStarted', this.currentSession);
    return sessionId;
  }

  /**
   * 结束当前会话
   */
  endSession(): void {
    if (this.currentSession) {
      this.currentSession.status = 'completed';
      this.currentSession.endTime = new Date();
      this.emit('sessionEnded', this.currentSession);
      this.currentSession = null;
    }
  }

  /**
   * 执行工具
   */
  async executeTool(toolName: string, parameters: any): Promise<ToolExecutionResult> {
    // 确保有活动会话
    if (!this.currentSession) {
      this.startSession();
    }

    const executionId = this.generateId();
    const execution: ToolExecutionState = {
      id: executionId,
      toolName,
      parameters,
      status: 'pending',
      startTime: new Date(),
      progress: 0
    };

    // 添加到当前会话
    this.currentSession!.executions.push(execution);
    this.currentSession!.status = 'executing';
    
    // 触发开始事件
    this.emit('executionStarted', execution);

    try {
      // 更新状态为执行中
      execution.status = 'executing';
      execution.progress = 10;
      this.emit('executionProgress', execution);

      // 执行工具
      const result = await VLImageToolsBridge.executeTool(toolName, parameters);
      
      // 更新执行状态
      execution.status = result.success ? 'completed' : 'failed';
      execution.endTime = new Date();
      execution.result = result;
      execution.progress = 100;

      // 添加到历史记录
      this.executionHistory.push({ ...execution });

      // 触发完成事件
      this.emit('executionCompleted', execution);

      // 检查会话是否完成
      if (this.currentSession && this.currentSession.executions.every(e => e.status !== 'executing')) {
        this.currentSession.status = 'completed';
        this.emit('sessionUpdated', this.currentSession);
      }

      return result;

    } catch (error) {
      // 处理执行错误
      execution.status = 'failed';
      execution.endTime = new Date();
      execution.result = {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        executionTime: Date.now() - execution.startTime.getTime(),
        toolName
      };
      execution.progress = 100;

      // 添加到历史记录
      this.executionHistory.push({ ...execution });

      // 触发失败事件
      this.emit('executionFailed', execution);

      return execution.result;
    }
  }

  /**
   * 获取可用工具列表
   */
  getAvailableTools() {
    return VLImageToolsBridge.getToolDefinitions().map(tool => ({
      name: tool.function.name,
      description: tool.function.description,
      parameters: tool.function.parameters?.properties || {}
    }));
  }

  /**
   * 获取当前会话
   */
  getCurrentSession(): ToolExecutionSession | null {
    return this.currentSession;
  }

  /**
   * 获取执行历史
   */
  getExecutionHistory(): ToolExecutionState[] {
    return [...this.executionHistory];
  }

  /**
   * 获取最近的执行结果
   */
  getLastExecution(): ToolExecutionState | null {
    return this.executionHistory.length > 0 
      ? this.executionHistory[this.executionHistory.length - 1] 
      : null;
  }

  /**
   * 清除历史记录
   */
  clearHistory(): void {
    this.executionHistory = [];
    this.emit('historyCleared');
  }

  /**
   * 取消当前执行（如果支持）
   */
  cancelExecution(executionId: string): boolean {
    if (this.currentSession) {
      const execution = this.currentSession.executions.find(e => e.id === executionId);
      if (execution && execution.status === 'executing') {
        execution.status = 'failed';
        execution.endTime = new Date();
        execution.result = {
          success: false,
          error: 'Execution cancelled by user',
          executionTime: Date.now() - execution.startTime.getTime(),
          toolName: execution.toolName
        };
        this.emit('executionCancelled', execution);
        return true;
      }
    }
    return false;
  }

  /**
   * 事件监听器管理
   */
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event callback for ${event}:`, error);
        }
      });
    }
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取工具执行统计
   */
  getExecutionStats() {
    const total = this.executionHistory.length;
    const successful = this.executionHistory.filter(e => e.result?.success).length;
    const failed = total - successful;
    
    const avgExecutionTime = total > 0 
      ? this.executionHistory.reduce((sum, e) => sum + (e.result?.executionTime || 0), 0) / total 
      : 0;

    const toolUsage = this.executionHistory.reduce((acc, e) => {
      acc[e.toolName] = (acc[e.toolName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      successful,
      failed,
      successRate: total > 0 ? (successful / total) * 100 : 0,
      avgExecutionTime: Math.round(avgExecutionTime),
      toolUsage
    };
  }
}

// 创建单例实例
export const toolController = new ToolController();
